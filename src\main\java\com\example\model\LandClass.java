package com.example.model;


public class LandClass {
    private int landClassId;
    private String landClassName;
    private String landClassLevel;
    private String landClassCode;
    private String landClassParent;
    private String landDescription;

    public LandClass(int landClassId, String landClassName, String landClassLevel, String landClassCode, String landClassParent, String landDescription) {
        this.landClassId = landClassId;
        this.landClassName = landClassName;
        this.landClassLevel = landClassLevel;
        this.landClassCode = landClassCode;
        this.landClassParent = landClassParent;
        this.landDescription = landDescription;
    }

    // Getters and Setters
    public int getLandClassId() {
        return landClassId;
    }

    public void setLandClassId(int landClassId) {
        this.landClassId = landClassId;
    }

    public String getLandClassName() {
        return landClassName;
    }

    public void setLandClassName(String landClassName) {
        this.landClassName = landClassName;
    }

    public String getLandClassLevel() {
        return landClassLevel;
    }

    public void setLandClassLevel(String landClassLevel) {
        this.landClassLevel = landClassLevel;
    }

    public String getLandClassCode() {
        return landClassCode;
    }

    public void setLandClassCode(String landClassCode) {
        this.landClassCode = landClassCode;
    }

    public String getLandClassParent() {
        return landClassParent;
    }

    public void setLandClassParent(String landClassParent) {
        this.landClassParent = landClassParent;
    }

    public String getLandDescription() {
        return landDescription;
    }

    public void setLandDescription(String landDescription) {
        this.landDescription = landDescription;
    }
}