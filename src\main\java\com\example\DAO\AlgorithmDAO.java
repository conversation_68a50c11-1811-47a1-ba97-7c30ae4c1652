package com.example.DAO;

import com.example.model.Algorithm;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class AlgorithmDAO {
    public List<Algorithm> getAllAlgorithms() {
        List<Algorithm> algorithmList = new ArrayList<>();
        String sql = "SELECT * FROM operatorinformation";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            while (rs.next()) {
                String operatorName = rs.getString("OperatorName");
                String operatorId = rs.getString("OperatorID");
                String operatorCategoryCode = rs.getString("OperatorTypeCode");
                String operatorInput = rs.getString("OperatorInput");
                String operatorOutput = rs.getString("OperatorOutput");
                String operatorDescription = rs.getString("OperatorDcp");
                String operatorAdaptability = rs.getString("Adaptation");
                Algorithm algorithm = new Algorithm(operatorName, operatorId, operatorCategoryCode, operatorInput, operatorOutput, operatorDescription, operatorAdaptability);
                algorithmList.add(algorithm);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return algorithmList;
    }
}