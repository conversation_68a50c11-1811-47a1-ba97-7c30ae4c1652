package com.example;

import com.example.DAO.*;
import com.example.model.*;
import org.kie.api.KieServices;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;

import java.util.Date;
import java.util.List;

public class Main {
    public static void main(String[] args) {
        // 创建业务需求对象
        BusinessRequirement requirement = new BusinessRequirement(
                new Date(),
                "Beijing",
                "Land Cover",
                "Monitoring",
                "Land Cover Change",
                "Classification",
                "None"
        );

        // 从数据库中获取数据
        BusinessKnowledgeDAO businessKnowledgeDAO = new BusinessKnowledgeDAO();
        List<BusinessKnowledge> businessKnowledgeList = businessKnowledgeDAO.getAllBusinessKnowledge();

        ImageDataDAO imageDataDAO = new ImageDataDAO();
        List<ImageData> imageDataList = imageDataDAO.getAllImageData();

        SensorDataDAO sensorDataDAO = new SensorDataDAO();
        List<SensorData> sensorDataList = sensorDataDAO.getAllSensorData();

        AlgorithmDAO algorithmDAO = new AlgorithmDAO();
        List<Algorithm> algorithmList = algorithmDAO.getAllAlgorithms();

        RegionDataDAO regionDataDAO = new RegionDataDAO();
        List<RegionData> regionDataList = regionDataDAO.getAllRegionData();

        LandClassDAO landClassDAO = new LandClassDAO();
        List<LandClass> landClassList = landClassDAO.getAllLandClass();
        


        // 加载 KieContainer
        KieServices kieServices = KieServices.Factory.get();
        KieContainer kieContainer = kieServices.getKieClasspathContainer();
        KieSession kieSession = kieContainer.newKieSession("businessSession");

        // 将业务需求对象插入 KieSession
        kieSession.insert(requirement);
        // 将从数据库获取的数据对象插入 KieSession
        for (BusinessKnowledge knowledge : businessKnowledgeList) {
            kieSession.insert(knowledge);
        }
        for (ImageData imageData : imageDataList) {
            kieSession.insert(imageData);
        }
        for (SensorData sensorData : sensorDataList) {
            kieSession.insert(sensorData);
        }
        for (Algorithm algorithm : algorithmList) {
            kieSession.insert(algorithm);
        }
        for (RegionData regionData : regionDataList) {
            kieSession.insert(regionData);
        }
        for (LandClass landClass : landClassList) {
            kieSession.insert(landClass);
        }

        // 触发规则
        kieSession.fireAllRules();

        // 关闭 KieSession
        kieSession.dispose();
    }
}