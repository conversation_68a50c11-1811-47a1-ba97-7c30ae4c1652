package com.example.model;


public class RegionData {
    private int regionId;
    private String regionName;
    private String regionCode;
    private String geometry;
    private String center;
    private int childrenNum;
    private String level;
    private String parentCode;
    private float minx;
    private float miny;
    private float maxx;
    private float maxy;

    public RegionData(int regionId, String regionName, String regionCode, String geometry, String center, int childrenNum, String level, String parentCode, float minx, float miny, float maxx, float maxy) {
        this.regionId = regionId;
        this.regionName = regionName;
        this.regionCode = regionCode;
        this.geometry = geometry;
        this.center = center;
        this.childrenNum = childrenNum;
        this.level = level;
        this.parentCode = parentCode;
        this.minx = minx;
        this.miny = miny;
        this.maxx = maxx;
        this.maxy = maxy;
    }

    // Getters and Setters
    public int getRegionId() {
        return regionId;
    }

    public void setRegionId(int regionId) {
        this.regionId = regionId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    public String getCenter() {
        return center;
    }

    public void setCenter(String center) {
        this.center = center;
    }

    public int getChildrenNum() {
        return childrenNum;
    }

    public void setChildrenNum(int childrenNum) {
        this.childrenNum = childrenNum;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public float getMinx() {
        return minx;
    }

    public void setMinx(float minx) {
        this.minx = minx;
    }

    public float getMiny() {
        return miny;
    }

    public void setMiny(float miny) {
        this.miny = miny;
    }

    public float getMaxx() {
        return maxx;
    }

    public void setMaxx(float maxx) {
        this.maxx = maxx;
    }

    public float getMaxy() {
        return maxy;
    }

    public void setMaxy(float maxy) {
        this.maxy = maxy;
    }
}