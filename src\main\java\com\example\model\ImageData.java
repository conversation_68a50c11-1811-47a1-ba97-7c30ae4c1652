package com.example.model;

import java.util.Date;

public class ImageData {
    private int dataId;
    private String dataName;
    private String dataType;
    private Date dataTime;
    private String dataRegion;
    private int sensorId;
    private int sensorType;
    private String dataDetailInfo;
    private float minx;
    private float miny;
    private float maxx;
    private float maxy;
    private float center;
    private String dataDescription;

    public ImageData(int dataId, String dataName, String dataType, Date dataTime, String dataRegion, int sensorId, int sensorType, String dataDetailInfo, float minx, float miny, float maxx, float maxy, float center, String dataDescription) {
        this.dataId = dataId;
        this.dataName = dataName;
        this.dataType = dataType;
        this.dataTime = dataTime;
        this.dataRegion = dataRegion;
        this.sensorId = sensorId;
        this.sensorType = sensorType;
        this.dataDetailInfo = dataDetailInfo;
        this.minx = minx;
        this.miny = miny;
        this.maxx = maxx;
        this.maxy = maxy;
        this.center = center;
        this.dataDescription = dataDescription;
    }

    // Getters and Setters
    public int getDataId() {
        return dataId;
    }

    public void setDataId(int dataId) {
        this.dataId = dataId;
    }

    public String getDataName() {
        return dataName;
    }

    public void setDataName(String dataName) {
        this.dataName = dataName;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Date getDataTime() {
        return dataTime;
    }

    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    public String getDataRegion() {
        return dataRegion;
    }

    public void setDataRegion(String dataRegion) {
        this.dataRegion = dataRegion;
    }

    public int getSensorId() {
        return sensorId;
    }

    public void setSensorId(int sensorId) {
        this.sensorId = sensorId;
    }

    public int getSensorType() {
        return sensorType;
    }

    public void setSensorType(int sensorType) {
        this.sensorType = sensorType;
    }

    public String getDataDetailInfo() {
        return dataDetailInfo;
    }

    public void setDataDetailInfo(String dataDetailInfo) {
        this.dataDetailInfo = dataDetailInfo;
    }

    public float getMinx() {
        return minx;
    }

    public void setMinx(float minx) {
        this.minx = minx;
    }

    public float getMiny() {
        return miny;
    }

    public void setMiny(float miny) {
        this.miny = miny;
    }

    public float getMaxx() {
        return maxx;
    }

    public void setMaxx(float maxx) {
        this.maxx = maxx;
    }

    public float getMaxy() {
        return maxy;
    }

    public void setMaxy(float maxy) {
        this.maxy = maxy;
    }

    public float getCenter() {
        return center;
    }

    public void setCenter(float center) {
        this.center = center;
    }

    public String getDataDescription() {
        return dataDescription;
    }

    public void setDataDescription(String dataDescription) {
        this.dataDescription = dataDescription;
    }
}