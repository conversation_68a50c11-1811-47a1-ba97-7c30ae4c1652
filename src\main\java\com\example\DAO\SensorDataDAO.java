package com.example.DAO;

import com.example.model.SensorData;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class SensorDataDAO {
    public List<SensorData> getAllSensorData() {
        List<SensorData> sensorDataList = new ArrayList<>();
        String sql = "SELECT * FROM image_sensor_info";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            while (rs.next()) {
                int sensorId = rs.getInt("SensorID");
                String sensorType = rs.getString("SensorType");
                String sensorName = rs.getString("Sensorname");
                String satelliteName = rs.getString("SatelliteName");
                String dataLevel = rs.getString("DataLevel");
                float spatialResolution = rs.getFloat("Spatialresolution");
                String band = rs.getString("Band");
                int breadth = rs.getInt("Breadth");
                String polarMode = rs.getString("Polarmode");
                String imageMode = rs.getString("Imagemode");
                SensorData sensorData = new SensorData(sensorId, sensorType, sensorName, satelliteName, dataLevel, spatialResolution, band, breadth, polarMode, imageMode);
                sensorDataList.add(sensorData);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sensorDataList;
    }
}