package com.example.model;

public class Algorithm {
    private String operatorName;
    private String operatorId;
    private String operatorCategoryCode;
    private String operatorInput;
    private String operatorOutput;
    private String operatorDescription;
    private String operatorAdaptability;

    public Algorithm(String operatorName, String operatorId, String operatorCategoryCode, String operatorInput, String operatorOutput, String operatorDescription, String operatorAdaptability) {
        this.operatorName = operatorName;
        this.operatorId = operatorId;
        this.operatorCategoryCode = operatorCategoryCode;
        this.operatorInput = operatorInput;
        this.operatorOutput = operatorOutput;
        this.operatorDescription = operatorDescription;
        this.operatorAdaptability = operatorAdaptability;
    }

    // Getters and Setters
    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorCategoryCode() {
        return operatorCategoryCode;
    }

    public void setOperatorCategoryCode(String operatorCategoryCode) {
        this.operatorCategoryCode = operatorCategoryCode;
    }

    public String getOperatorInput() {
        return operatorInput;
    }

    public void setOperatorInput(String operatorInput) {
        this.operatorInput = operatorInput;
    }

    public String getOperatorOutput() {
        return operatorOutput;
    }

    public void setOperatorOutput(String operatorOutput) {
        this.operatorOutput = operatorOutput;
    }

    public String getOperatorDescription() {
        return operatorDescription;
    }

    public void setOperatorDescription(String operatorDescription) {
        this.operatorDescription = operatorDescription;
    }

    public String getOperatorAdaptability() {
        return operatorAdaptability;
    }

    public void setOperatorAdaptability(String operatorAdaptability) {
        this.operatorAdaptability = operatorAdaptability;
    }
}    