package com.example.DAO;

import com.example.model.RegionData;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class RegionDataDAO {
    public List<RegionData> getAllRegionData() {
        List<RegionData> regionDataList = new ArrayList<>();
        String sql = "SELECT * FROM province_city_boundary";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            while (rs.next()) {
                int regionId = rs.getInt("RegionID");
                String regionName = rs.getString("RegionName");
                String regionCode = rs.getString("RegionCode");
            }
        }
        return regionDataList;
    }
}