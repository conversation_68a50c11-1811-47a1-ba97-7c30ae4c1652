package com.example.model;

public class BusinessKnowledge {
    private int businessId;
    private String prephaseLandCover;
    private String postphaseLandCover;
    private String dataSourceRequirements;
    private int minAreaOfThePatches;
    private String linkedDataset;
    private String businessUnits;
    private String businessType;

    public BusinessKnowledge(int businessId, String prephaseLandCover, String postphaseLandCover, String dataSourceRequirements, int minAreaOfThePatches, String linkedDataset, String businessUnits, String businessType) {
        this.businessId = businessId;
        this.prephaseLandCover = prephaseLandCover;
        this.postphaseLandCover = postphaseLandCover;
        this.dataSourceRequirements = dataSourceRequirements;
        this.minAreaOfThePatches = minAreaOfThePatches;
        this.linkedDataset = linkedDataset;
        this.businessUnits = businessUnits;
        this.businessType = businessType;
    }

    // Getters and Setters
    public int getBusinessId() {
        return businessId;
    }

    public void setBusinessId(int businessId) {
        this.businessId = businessId;
    }

    public String getPrephaseLandCover() {
        return prephaseLandCover;
    }

    public void setPrephaseLandCover(String prephaseLandCover) {
        this.prephaseLandCover = prephaseLandCover;
    }

    public String getPostphaseLandCover() {
        return postphaseLandCover;
    }

    public void setPostphaseLandCover(String postphaseLandCover) {
        this.postphaseLandCover = postphaseLandCover;
    }

    public String getDataSourceRequirements() {
        return dataSourceRequirements;
    }

    public void setDataSourceRequirements(String dataSourceRequirements) {
        this.dataSourceRequirements = dataSourceRequirements;
    }

    public int getMinAreaOfThePatches() {
        return minAreaOfThePatches;
    }

    public void setMinAreaOfThePatches(int minAreaOfThePatches) {
        this.minAreaOfThePatches = minAreaOfThePatches;
    }

    public String getLinkedDataset() {
        return linkedDataset;
    }

    public void setLinkedDataset(String linkedDataset) {
        this.linkedDataset = linkedDataset;
    }

    public String getBusinessUnits() {
        return businessUnits;
    }

    public void setBusinessUnits(String businessUnits) {
        this.businessUnits = businessUnits;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}