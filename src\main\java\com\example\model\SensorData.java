package com.example.model;

public class SensorData {
    private int sensorId;
    private String sensorType;
    private String sensorName;
    private String satelliteName;
    private String dataLevel;
    private float spatialResolution;
    private String band;
    private int breadth;
    private String polarMode;
    private String imageMode;

    public SensorData(int sensorId, String sensorType, String sensorName, String satelliteName, String dataLevel, float spatialResolution, String band, int breadth, String polarMode, String imageMode) {
        this.sensorId = sensorId;
        this.sensorType = sensorType;
        this.sensorName = sensorName;
        this.satelliteName = satelliteName;
        this.dataLevel = dataLevel;
        this.spatialResolution = spatialResolution;
        this.band = band;
        this.breadth = breadth;
        this.polarMode = polarMode;
        this.imageMode = imageMode;
    }

    // Getters and Setters
    public int getSensorId() {
        return sensorId;
    }

    public void setSensorId(int sensorId) {
        this.sensorId = sensorId;
    }

    public String getSensorType() {
        return sensorType;
    }

    public void setSensorType(String sensorType) {
        this.sensorType = sensorType;
    }

    public String getSensorName() {
        return sensorName;
    }

    public void setSensorName(String sensorName) {
        this.sensorName = sensorName;
    }

    public String getSatelliteName() {
        return satelliteName;
    }

    public void setSatelliteName(String satelliteName) {
        this.satelliteName = satelliteName;
    }

    public String getDataLevel() {
        return dataLevel;
    }

    public void setDataLevel(String dataLevel) {
        this.dataLevel = dataLevel;
    }

    public float getSpatialResolution() {
        return spatialResolution;
    }

    public void setSpatialResolution(float spatialResolution) {
        this.spatialResolution = spatialResolution;
    }

    public String getBand() {
        return band;
    }

    public void setBand(String band) {
        this.band = band;
    }

    public int getBreadth() {
        return breadth;
    }

    public void setBreadth(int breadth) {
        this.breadth = breadth;
    }

    public String getPolarMode() {
        return polarMode;
    }

    public void setPolarMode(String polarMode) {
        this.polarMode = polarMode;
    }

    public String getImageMode() {
        return imageMode;
    }

    public void setImageMode(String imageMode) {
        this.imageMode = imageMode;
    }
}
