package com.example.model;

import java.util.Date;

public class BusinessRequirement {
    private Date time;
    private String location;
    private String monitoringElement;
    private String businessType;
    private String monitoringContent;
    private String algorithmRequirement;
    private String auxiliaryFeatureRequirement;

    public BusinessRequirement(Date time, String location, String monitoringElement, String businessType, String monitoringContent, String algorithmRequirement, String auxiliaryFeatureRequirement) {
        this.time = time;
        this.location = location;
        this.monitoringElement = monitoringElement;
        this.businessType = businessType;
        this.monitoringContent = monitoringContent;
        this.algorithmRequirement = algorithmRequirement;
        this.auxiliaryFeatureRequirement = auxiliaryFeatureRequirement;
    }

    // Getters and Setters
    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMonitoringElement() {
        return monitoringElement;
    }

    public void setMonitoringElement(String monitoringElement) {
        this.monitoringElement = monitoringElement;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getMonitoringContent() {
        return monitoringContent;
    }

    public void setMonitoringContent(String monitoringContent) {
        this.monitoringContent = monitoringContent;
    }

    public String getAlgorithmRequirement() {
        return algorithmRequirement;
    }

    public void setAlgorithmRequirement(String algorithmRequirement) {
        this.algorithmRequirement = algorithmRequirement;
    }

    public String getAuxiliaryFeatureRequirement() {
        return auxiliaryFeatureRequirement;
    }

    public void setAuxiliaryFeatureRequirement(String auxiliaryFeatureRequirement) {
        this.auxiliaryFeatureRequirement = auxiliaryFeatureRequirement;
    }
}