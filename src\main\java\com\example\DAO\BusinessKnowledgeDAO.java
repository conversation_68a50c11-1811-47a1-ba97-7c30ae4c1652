package com.example.DAO;

import com.example.model.BusinessKnowledge;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class BusinessKnowledgeDAO {
    public List<BusinessKnowledge> getAllBusinessKnowledge() {
        List<BusinessKnowledge> businessKnowledgeList = new ArrayList<>();
        String sql = "SELECT * FROM business_info";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            while (rs.next()) {
                int businessId = rs.getInt("BusinessID");
                String prephaseLandCover = rs.getString("PrephaseLandCover");
                String postphaseLandCover = rs.getString("PostphaseLandCover");
                String dataSourceRequirements = rs.getString("DataSourceRequirements");
                int minAreaOfThePatches = rs.getInt("MinAreaofthePatches");
                String linkedDataset = rs.getString("LinkedDataset");
                String businessUnits = rs.getString("BusinessUnits");
                String businessType = rs.getString("BusinessType");
                BusinessKnowledge businessKnowledge = new BusinessKnowledge(businessId, prephaseLandCover, postphaseLandCover, dataSourceRequirements, minAreaOfThePatches, linkedDataset, businessUnits, businessType);
                businessKnowledgeList.add(businessKnowledge);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return businessKnowledgeList;
    }
}