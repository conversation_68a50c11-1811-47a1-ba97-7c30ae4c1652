package com.example.rules

import com.example.model.BusinessRequirement;
import com.example.model.BusinessKnowledge;
import com.example.model.ImageData;
import com.example.model.SensorData;
import com.example.model.Algorithm;
import com.example.model.RegionData;

rule "Filter Business Knowledge"
    when
        $requirement: BusinessRequirement()
        $knowledge: BusinessKnowledge(businessType == $requirement.getBusinessType())
    then
        System.out.println("Matched Business Knowledge: " + $knowledge.getBusinessId());
end

rule "Filter Image Data"
    when
        $requirement: BusinessRequirement()
        $knowledge: BusinessKnowledge(businessType == $requirement.getBusinessType())
        $region: RegionData(regionName == $requirement.getLocation())
        $imageData: ImageData(dataTime == $requirement.getTime(), dataRegion.contains($region.getGeometry()))
        $sensorData: SensorData(sensorId == $imageData.getSensorId())
    then
        System.out.println("Matched Image Data: " + $imageData.getDataId());
end

rule "Filter Algorithm"
    when
        $requirement: BusinessRequirement()
        $knowledge: BusinessKnowledge(businessType == $requirement.getBusinessType())
        $algorithm: Algorithm(operatorCategoryCode == $requirement.getAlgorithmRequirement())
    then
        System.out.println("Matched Algorithm: " + $algorithm.getOperatorName());
end