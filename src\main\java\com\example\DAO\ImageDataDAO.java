package com.example.DAO;

import com.example.model.ImageData;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ImageDataDAO {
    public List<ImageData> getAllImageData() {
        List<ImageData> imageDataList = new ArrayList<>();
        String sql = "SELECT * FROM image_info";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            while (rs.next()) {
                int dataId = rs.getInt("DataID");
                String dataName = rs.getString("DataName");
                String dataType = rs.getString("DataType");
                java.sql.Date dataTime = rs.getDate("DataTime");
                String dataRegion = rs.getString("DataRegion");
                int sensorId = rs.getInt("SensorID");
                int sensorType = rs.getInt("SensorType");
                String dataDetailInfo = rs.getString("DataDetailInfo");
                float minx = rs.getFloat("Minx");
                float miny = rs.getFloat("Miny");
                float maxx = rs.getFloat("Maxx");
                float maxy = rs.getFloat("Maxy");
                float center = rs.getFloat("Center");
                String dataDescription = rs.getString("DataDescription");
                ImageData imageData = new ImageData(dataId, dataName, dataType, dataTime, dataRegion, sensorId, sensorType, dataDetailInfo, minx, miny, maxx, maxy, center, dataDescription);
                imageDataList.add(imageData);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return imageDataList;
    }
}